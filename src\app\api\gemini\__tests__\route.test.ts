import { POST } from '../route'; // Ajusta la ruta según tu estructura
import { NextRequest } from 'next/server';
import { generarMapaMental } from '../../../lib/gemini/mindMapGenerator'; // Mocked

// Mockear la función generarMapaMental
jest.mock('../../../lib/gemini/mindMapGenerator', () => ({
  generarMapaMental: jest.fn(),
}));

describe('/api/ai POST endpoint - Mind Map Generation', () => {
  const mockGenerarMapaMental = generarMapaMental as jest.Mock;

  beforeEach(() => {
    // Limpiar mocks antes de cada prueba
    mockGenerarMapaMental.mockReset();
  });

  // Helper para crear un mock de NextRequest
  const createMockAIRequest = (body: any): NextRequest => {
    const headers = new Headers({ 'Content-Type': 'application/json' });
    const mockReq = {
      headers,
      json: async () => body,
      // Añade otros métodos/propiedades de NextRequest que uses si es necesario
    } as unknown as NextRequest;
    return mockReq;
  };

  test('Generación Exitosa de Mapa Mental (HTML básico)', async () => {
    const mockHtmlResponse = '<!DOCTYPE html><html><head><title>Test Mind Map</title></head><body><h1>Mind Map</h1></body></html>';
    mockGenerarMapaMental.mockResolvedValue(mockHtmlResponse);

    const mockRequestBody = {
      action: 'generarMapaMental',
      contextos: [{ titulo: 'Doc 1', contenido: 'Contenido del doc 1' }],
      peticion: 'Crear un mapa mental simple',
    };
    const mockRequest = createMockGeminiRequest(mockRequestBody);

    const response = await POST(mockRequest);
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody.success).toBe(true);
    expect(typeof responseBody.result).toBe('string');
    expect(responseBody.result.startsWith('<!DOCTYPE html>') || responseBody.result.startsWith('<html')).toBe(true);
    expect(mockGenerarMapaMental).toHaveBeenCalledTimes(1);
    expect(mockGenerarMapaMental).toHaveBeenCalledWith(
      mockRequestBody.contextos,
      mockRequestBody.peticion
    );
  });

  test('Error cuando la acción no es generarMapaMental', async () => {
    const mockRequestBody = {
      action: 'accionDesconocida',
      contextos: [],
      peticion: '',
    };
    const mockRequest = createMockGeminiRequest(mockRequestBody);

    const response = await POST(mockRequest);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.success).toBe(false);
    expect(responseBody.error).toBe('Acción no válida');
    expect(mockGenerarMapaMental).not.toHaveBeenCalled();
  });

  test('Error cuando generarMapaMental falla', async () => {
    mockGenerarMapaMental.mockRejectedValue(new Error('Error de Gemini'));

    const mockRequestBody = {
      action: 'generarMapaMental',
      contextos: [{ titulo: 'Doc 1', contenido: 'Contenido' }],
      peticion: 'Crear mapa',
    };
    const mockRequest = createMockGeminiRequest(mockRequestBody);

    const response = await POST(mockRequest);
    const responseBody = await response.json();

    expect(response.status).toBe(500);
    expect(responseBody.success).toBe(false);
    expect(responseBody.error).toBe('Error al generar el mapa mental');
    expect(responseBody.details).toBe('Error de Gemini');
    expect(mockGenerarMapaMental).toHaveBeenCalledTimes(1);
  });
  
  test('Error cuando falta la acción en la petición', async () => {
    const mockRequestBody = {
      // action: undefined, // Falta la acción
      contextos: [{ titulo: 'Doc 1', contenido: 'Contenido' }],
      peticion: 'Crear mapa',
    };
    const mockRequest = createMockGeminiRequest(mockRequestBody);

    const response = await POST(mockRequest);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.success).toBe(false);
    expect(responseBody.error).toBe('Acción no válida');
    expect(mockGenerarMapaMental).not.toHaveBeenCalled();
  });

  test('Error cuando faltan los contextos para generarMapaMental', async () => {
    const mockRequestBody = {
      action: 'generarMapaMental',
      // contextos: undefined, // Faltan contextos
      peticion: 'Crear mapa',
    };
    const mockRequest = createMockGeminiRequest(mockRequestBody);

    const response = await POST(mockRequest);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.success).toBe(false);
    expect(responseBody.error).toBe('Los contextos son requeridos para generarMapaMental');
    expect(mockGenerarMapaMental).not.toHaveBeenCalled();
  });

});
