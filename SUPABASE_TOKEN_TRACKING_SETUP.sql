-- ============================================
-- SCRIPT SQL PARA TRACKING DE TOKENS EN SUPABASE
-- ============================================
-- Ejecutar este script en el SQL Editor de Supabase

-- 1. Crear tabla para tracking de uso de tokens
CREATE TABLE IF NOT EXISTS user_token_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type VARCHAR(100) NOT NULL,
  model_name VARCHAR(50) NOT NULL,
  prompt_tokens INTEGER NOT NULL,
  completion_tokens INTEGER NOT NULL,
  total_tokens INTEGER NOT NULL,
  estimated_cost DECIMAL(10,8) NOT NULL,
  usage_month DATE NOT NULL DEFAULT DATE_TRUNC('month', NOW()),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Crear tabla para perfiles de usuario y límites
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  subscription_plan VARCHAR(20) DEFAULT 'free',
  monthly_token_limit INTEGER DEFAULT 50000,
  current_month_tokens INTEGER DEFAULT 0,
  current_month DATE DEFAULT DATE_TRUNC('month', NOW()),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Crear índices para optimización
CREATE INDEX IF NOT EXISTS idx_user_token_usage_user_month 
  ON user_token_usage(user_id, usage_month);

CREATE INDEX IF NOT EXISTS idx_user_token_usage_model 
  ON user_token_usage(model_name);

CREATE INDEX IF NOT EXISTS idx_user_token_usage_activity 
  ON user_token_usage(activity_type);

CREATE INDEX IF NOT EXISTS idx_user_token_usage_created 
  ON user_token_usage(created_at);

CREATE INDEX IF NOT EXISTS idx_user_profiles_user 
  ON user_profiles(user_id);

-- 4. Habilitar Row Level Security (RLS)
ALTER TABLE user_token_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- 5. Crear políticas de seguridad para user_token_usage
CREATE POLICY "Users can view their own token usage" 
  ON user_token_usage FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own token usage" 
  ON user_token_usage FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- 6. Crear políticas de seguridad para user_profiles
CREATE POLICY "Users can view their own profile" 
  ON user_profiles FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" 
  ON user_profiles FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own profile" 
  ON user_profiles FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- 7. Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 8. Trigger para actualizar updated_at en user_profiles
CREATE TRIGGER update_user_profiles_updated_at 
  BEFORE UPDATE ON user_profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. Función para obtener estadísticas mensuales del usuario
CREATE OR REPLACE FUNCTION get_user_monthly_stats(target_user_id UUID DEFAULT auth.uid())
RETURNS TABLE (
  total_sessions BIGINT,
  total_tokens BIGINT,
  total_cost NUMERIC,
  current_month_tokens INTEGER,
  monthly_limit INTEGER,
  subscription_plan TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::BIGINT as total_sessions,
    COALESCE(SUM(uto.total_tokens), 0)::BIGINT as total_tokens,
    COALESCE(SUM(uto.estimated_cost), 0)::NUMERIC as total_cost,
    COALESCE(up.current_month_tokens, 0) as current_month_tokens,
    COALESCE(up.monthly_token_limit, 50000) as monthly_limit,
    COALESCE(up.subscription_plan, 'free') as subscription_plan
  FROM user_token_usage uto
  LEFT JOIN user_profiles up ON up.user_id = target_user_id
  WHERE uto.user_id = target_user_id
    AND uto.usage_month = DATE_TRUNC('month', NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Verificar que las tablas se crearon correctamente
SELECT 
  'user_token_usage' as table_name,
  COUNT(*) as record_count
FROM user_token_usage
UNION ALL
SELECT 
  'user_profiles' as table_name,
  COUNT(*) as record_count  
FROM user_profiles;

-- ============================================
-- INSTRUCCIONES DE USO:
-- ============================================
-- 1. Copia y pega este script completo en el SQL Editor de Supabase
-- 2. Ejecuta el script
-- 3. Verifica que no hay errores
-- 4. Las tablas y políticas estarán listas para usar
-- ============================================
