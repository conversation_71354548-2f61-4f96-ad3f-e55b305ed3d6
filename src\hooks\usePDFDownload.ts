'use client';

import { useCallback } from 'react';
import { toast } from 'react-hot-toast';

interface PDFOptions {
  filename?: string;
  title?: string;
  author?: string;
  subject?: string;
}

// Función para aplicar separación silábica con guiones blandos
const applyHyphenation = async (element: HTMLElement) => {
  try {
    // Importación dinámica de hyphen para español (versión síncrona)
    const { hyphenateSync } = await import('hyphen/es');

    // Función recursiva para procesar nodos de texto
    const processTextNodes = (node: Node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent || '';
        if (text.trim()) {
          // Aplicar separación silábica con guiones blandos (\u00AD)
          const hyphenatedText = hyphenateSync(text, {
            hyphenChar: '\u00AD', // <PERSON><PERSON><PERSON> blando (soft hyphen)
            minWordLength: 5 // Solo palabras de 5+ caracteres
          });
          node.textContent = hyphenatedText;
        }
      } else {
        // Procesar recursivamente los nodos hijos
        Array.from(node.childNodes).forEach(processTextNodes);
      }
    };

    processTextNodes(element);
  } catch (error) {
    console.warn('Error al aplicar separación silábica:', error);
    // Si falla la separación silábica, continuar sin ella
  }
};

export const usePDFDownload = () => {
  const downloadFromHTML = useCallback(async (
    htmlContent: string,
    options: PDFOptions = {}
  ) => {
    try {
      // Verificar que estamos en el cliente
      if (typeof window === 'undefined') {
        console.error('PDF download solo funciona en el cliente');
        return;
      }

      // Importación dinámica de html2pdf.js para evitar errores de SSR
      const html2pdf = (await import('html2pdf.js')).default;

      // 1. Crear un elemento temporal en el DOM para renderizar el HTML
      const element = document.createElement('div');
      element.innerHTML = htmlContent;

      // 2. Aplicar estilos para que se vea profesional
      element.style.width = '210mm'; // Ancho de un A4
      element.style.padding = '20mm'; // Márgenes de 20mm
      element.style.fontFamily = 'Helvetica, Arial, sans-serif';
      element.style.fontSize = '11pt';
      element.style.lineHeight = '1.6';
      element.style.textAlign = 'justify'; // ¡Justificación automática!
      element.style.boxSizing = 'border-box';
      element.style.backgroundColor = 'white';
      element.style.color = '#333';

      // Estilos específicos para elementos
      const style = document.createElement('style');
      style.textContent = `
        h1 {
          font-size: 18pt;
          color: #2563eb;
          margin: 0 0 15pt 0;
          border-bottom: 2px solid #2563eb;
          padding-bottom: 8pt;
          text-align: left;
          font-weight: bold;
        }
        h2 {
          font-size: 16pt;
          color: #1e40af;
          margin: 20pt 0 12pt 0;
          border-bottom: 1px solid #cbd5e1;
          padding-bottom: 5pt;
          text-align: left;
          font-weight: bold;
        }
        h3 {
          font-size: 14pt;
          color: #1e40af;
          margin: 15pt 0 8pt 0;
          text-align: left;
          font-weight: bold;
        }
        p {
          margin: 8pt 0;
          text-align: justify;
          text-justify: inter-word;
          hyphens: auto;
          word-wrap: break-word;
        }
        strong {
          color: #1e40af;
          font-weight: bold;
        }
        em {
          font-style: italic;
          color: #64748b;
        }
        ul, ol {
          margin: 8pt 0;
          padding-left: 15pt;
        }
        li {
          margin: 4pt 0;
          text-align: justify;
        }
        blockquote {
          margin: 12pt 0;
          padding: 8pt 12pt;
          background-color: #f8fafc;
          border-left: 4pt solid #2563eb;
          font-style: italic;
          text-align: justify;
        }
        .metadata {
          font-size: 9pt;
          color: #64748b;
          margin-bottom: 20pt;
          padding: 8pt;
          background-color: #f8fafc;
          border-radius: 3pt;
          border: 1pt solid #e2e8f0;
          text-align: left;
        }
        .metadata p {
          margin: 2pt 0;
          text-align: left;
        }
      `;
      element.appendChild(style);

      // 2.5. Aplicar separación silábica antes de generar el PDF
      await applyHyphenation(element);

      // 3. Opciones de html2pdf optimizadas
      const pdfOptions = {
        margin: [10, 10, 10, 10], // El padding del div ya actúa como margen
        filename: options.filename || `documento-${new Date().toISOString().split('T')[0]}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          scrollX: 0,
          scrollY: 0
        },
        jsPDF: {
          unit: 'mm',
          format: 'a4',
          orientation: 'portrait',
          putOnlyUsedFonts: true,
          floatPrecision: 16
        },
        // Habilita el modo inteligente para evitar cortes
        pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
      };

      // 4. Generar y descargar el PDF
      await html2pdf().from(element).set(pdfOptions).save();

      toast.success('PDF descargado exitosamente');

    } catch (error) {
      console.error('Error al generar PDF con html2pdf:', error);
      toast.error('Error al generar el PDF');
    }
  }, []);

  // La función downloadFromText ahora convierte texto plano en HTML y reutiliza downloadFromHTML
  const downloadFromText = useCallback((
    content: string,
    options: PDFOptions = {}
  ) => {
    // Convierte párrafos de texto plano en párrafos HTML
    const htmlContent = content
      .split(/\n\s*\n/)
      .filter(p => p.trim())
      .map(p => `<p>${p.trim()}</p>`)
      .join('');

    // Reutiliza la función de HTML que es más robusta
    return downloadFromHTML(`<div>${htmlContent}</div>`, options);
  }, [downloadFromHTML]);

  return {
    downloadFromHTML,
    downloadFromText
  };
};