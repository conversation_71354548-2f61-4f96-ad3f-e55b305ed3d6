'use client';

import { useCallback } from 'react';
import { toast } from 'react-hot-toast';

interface PDFOptions {
  filename?: string;
  title?: string;
  author?: string;
  subject?: string;
}

// Función para crear HTML optimizado para PDF con mejor paginación
const createOptimizedHTML = (htmlContent: string, title?: string) => {
  // Limpiar y estructurar el contenido
  const cleanContent = htmlContent
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
    .replace(/\s+/g, ' ')
    .trim();

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>${title || 'Resumen'}</title>
      <style>
        @page {
          size: A4;
          margin: 20mm;
        }

        * {
          box-sizing: border-box;
          margin: 0;
          padding: 0;
        }

        body {
          font-family: 'Helvetica', 'Arial', sans-serif;
          font-size: 11pt;
          line-height: 1.6;
          color: #333;
          background: white;
          word-wrap: break-word;
          overflow-wrap: break-word;
          hyphens: auto;
          -webkit-hyphens: auto;
          -moz-hyphens: auto;
          -ms-hyphens: auto;
        }

        .container {
          max-width: 100%;
          margin: 0 auto;
          padding: 0;
        }

        .header {
          margin-bottom: 20pt;
          padding-bottom: 10pt;
          border-bottom: 2px solid #2563eb;
          page-break-after: avoid;
        }

        .title {
          font-size: 18pt;
          font-weight: bold;
          color: #2563eb;
          margin-bottom: 10pt;
          text-align: center;
        }

        .metadata {
          font-size: 9pt;
          color: #64748b;
          background-color: #f8fafc;
          padding: 8pt;
          border-radius: 3pt;
          border: 1pt solid #e2e8f0;
          margin-bottom: 15pt;
        }

        .content {
          text-align: justify;
          text-justify: inter-word;
        }

        h1, h2, h3, h4, h5, h6 {
          page-break-after: avoid;
          page-break-inside: avoid;
          margin-top: 15pt;
          margin-bottom: 8pt;
          font-weight: bold;
        }

        h1 {
          font-size: 16pt;
          color: #2563eb;
          border-bottom: 1px solid #cbd5e1;
          padding-bottom: 5pt;
        }

        h2 {
          font-size: 14pt;
          color: #1e40af;
        }

        h3 {
          font-size: 12pt;
          color: #1e40af;
        }

        p {
          margin-bottom: 8pt;
          text-align: justify;
          orphans: 2;
          widows: 2;
          word-wrap: break-word;
          overflow-wrap: break-word;
        }

        ul, ol {
          margin: 8pt 0;
          padding-left: 15pt;
        }

        li {
          margin: 4pt 0;
          text-align: justify;
          orphans: 2;
          widows: 2;
        }

        blockquote {
          margin: 12pt 0;
          padding: 8pt 12pt;
          background-color: #f8fafc;
          border-left: 4pt solid #2563eb;
          font-style: italic;
          page-break-inside: avoid;
        }

        strong, b {
          font-weight: bold;
          color: #1e40af;
        }

        em, i {
          font-style: italic;
          color: #64748b;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          margin: 8pt 0;
          page-break-inside: avoid;
        }

        th, td {
          border: 1pt solid #e2e8f0;
          padding: 4pt 8pt;
          text-align: left;
        }

        th {
          background-color: #f8fafc;
          font-weight: bold;
        }

        .page-break {
          page-break-before: always;
        }

        .avoid-break {
          page-break-inside: avoid;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="title">${title || 'Resumen'}</div>
          <div class="metadata">
            <div><strong>Fecha de creación:</strong> ${new Date().toLocaleDateString('es-ES', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</div>
            <div><strong>Autor:</strong> OposiAI</div>
            <div><strong>Instrucciones utilizadas:</strong> Crea un resumen completo y estructurado del tema</div>
          </div>
        </div>
        <div class="content">
          ${cleanContent}
        </div>
      </div>
    </body>
    </html>
  `;
};

export const usePDFDownloadAdvanced = () => {
  const downloadFromHTML = useCallback(async (
    htmlContent: string,
    options: PDFOptions = {}
  ) => {
    try {
      // Verificar que estamos en el cliente
      if (typeof window === 'undefined') {
        console.error('PDF download solo funciona en el cliente');
        return;
      }

      toast.loading('Generando PDF avanzado...');

      // Crear HTML optimizado
      const optimizedHTML = createOptimizedHTML(htmlContent, options.title);

      // Crear elemento temporal con el HTML optimizado
      const element = document.createElement('div');
      element.innerHTML = optimizedHTML;
      element.style.position = 'absolute';
      element.style.left = '-9999px';
      element.style.top = '0';
      element.style.width = '210mm';
      element.style.background = 'white';
      document.body.appendChild(element);

      // Importar jsPDF y html2canvas dinámicamente
      const [{ default: jsPDF }, { default: html2canvas }] = await Promise.all([
        import('jspdf'),
        import('html2canvas')
      ]);

      // Configuración optimizada para html2canvas
      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 794, // A4 width in pixels at 96 DPI
        height: 1123, // A4 height in pixels at 96 DPI
        scrollX: 0,
        scrollY: 0,
        logging: false
      });

      // Crear PDF con jsPDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        compress: true
      });

      const imgData = canvas.toDataURL('image/jpeg', 0.95);
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Agregar primera página
      pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Agregar páginas adicionales si es necesario
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Limpiar elemento temporal
      document.body.removeChild(element);

      // Descargar PDF
      const filename = options.filename || `resumen-${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(filename);

      toast.dismiss();
      toast.success('PDF generado exitosamente con tecnología avanzada');

    } catch (error) {
      toast.dismiss();
      console.error('Error al generar PDF avanzado:', error);
      toast.error('Error al generar el PDF avanzado');
    }
  }, []);

  const downloadFromText = useCallback((
    content: string,
    options: PDFOptions = {}
  ) => {
    // Convertir texto plano a HTML simple
    const htmlContent = content
      .split(/\n\s*\n/)
      .filter(p => p.trim())
      .map(p => `<p>${p.trim()}</p>`)
      .join('');

    return downloadFromHTML(htmlContent, options);
  }, [downloadFromHTML]);

  return {
    downloadFromHTML,
    downloadFromText
  };
};
