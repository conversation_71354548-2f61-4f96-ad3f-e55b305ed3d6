import { POST } from '../route'; // Ajusta la ruta según tu estructura
import { NextRequest } from 'next/server';
import { generarMapaMental } from '../../../lib/gemini/mindMapGenerator'; // Mocked

// Mockear la función generarMapaMental
jest.mock('../../../lib/gemini/mindMapGenerator', () => ({
  generarMapaMental: jest.fn(),
}));

describe('/api/ai POST endpoint - Mind Map Generation', () => {
  const mockGenerarMapaMental = generarMapaMental as jest.Mock;

  beforeEach(() => {
    // Limpiar mocks antes de cada prueba
    mockGenerarMapaMental.mockReset();
  });

  // Helper para crear un mock de NextRequest
  const createMockAIRequest = (body: any): NextRequest => {
    const headers = new Headers({ 'Content-Type': 'application/json' });
    const mockReq = {
      headers,
      json: async () => body,
      // Añade otros métodos/propiedades de NextRequest que uses si es necesario
    } as unknown as NextRequest;
    return mockReq;
  };

  test('Genera mapa mental exitosamente', async () => {
    // Configurar el mock para devolver un resultado exitoso
    const mockResult = {
      treeData: [
        {
          name: 'Concepto Principal',
          children: [
            { name: 'Subconcepto 1' },
            { name: 'Subconcepto 2' }
          ]
        }
      ]
    };
    mockGenerarMapaMental.mockResolvedValue(mockResult);

    const mockRequestBody = {
      action: 'generarMapaMental',
      contextos: ['Contenido del documento'],
      peticion: 'Crear mapa mental sobre el tema',
    };
    const mockRequest = createMockAIRequest(mockRequestBody);

    const response = await POST(mockRequest);
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody.success).toBe(true);
    expect(responseBody.result).toEqual(mockResult);
    expect(mockGenerarMapaMental).toHaveBeenCalledWith(
      'Crear mapa mental sobre el tema',
      ['Contenido del documento']
    );
  });

  test('Error cuando generarMapaMental falla', async () => {
    // Configurar el mock para lanzar un error
    const errorMessage = 'Error al generar mapa mental';
    mockGenerarMapaMental.mockRejectedValue(new Error(errorMessage));

    const mockRequestBody = {
      action: 'generarMapaMental',
      contextos: ['Contenido del documento'],
      peticion: 'Crear mapa mental sobre el tema',
    };
    const mockRequest = createMockAIRequest(mockRequestBody);

    const response = await POST(mockRequest);
    const responseBody = await response.json();

    expect(response.status).toBe(500);
    expect(responseBody.success).toBe(false);
    expect(responseBody.error).toBe('Error interno del servidor');
    expect(mockGenerarMapaMental).toHaveBeenCalledWith(
      'Crear mapa mental sobre el tema',
      ['Contenido del documento']
    );
  });

  test('Error cuando falta la acción en la petición', async () => {
    const mockRequestBody = {
      // action: undefined, // Falta la acción
      contextos: [{ titulo: 'Doc 1', contenido: 'Contenido' }],
      peticion: 'Crear mapa',
    };
    const mockRequest = createMockAIRequest(mockRequestBody);

    const response = await POST(mockRequest);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.error).toBe('Datos inválidos');
    expect(mockGenerarMapaMental).not.toHaveBeenCalled();
  });

  test('Error cuando faltan los contextos para generarMapaMental', async () => {
    const mockRequestBody = {
      action: 'generarMapaMental',
      // contextos: undefined, // Faltan contextos
      peticion: 'Crear mapa',
    };
    const mockRequest = createMockAIRequest(mockRequestBody);

    const response = await POST(mockRequest);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.error).toBe('Datos inválidos');
    expect(mockGenerarMapaMental).not.toHaveBeenCalled();
  });

});
